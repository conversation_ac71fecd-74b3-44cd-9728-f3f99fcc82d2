import api from './api';

// Sales Order Types
export interface SalesOrderCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface SalesOrderItem {
  id?: number;
  product?: number;
  product_name?: string;
  description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  line_order?: number;
}

export interface SalesOrder {
  id?: number;
  so_id: string;
  so_number: string;
  customer: number;
  customer_details?: SalesOrderCustomer;
  customer_name?: string; // Computed field for display
  so_date: string;
  expected_date?: string;
  
  // Seller Information
  seller_name?: string;
  seller_email?: string;
  seller_phone?: string;
  
  // Financial Information
  subtotal: number;
  discount_percent: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  amount_delivered: number;
  balance_due: number;
  
  // Settings
  status: 'draft' | 'pending' | 'sent' | 'acknowledged' | 'partial' | 'delivered' | 'closed' | 'cancelled';
  payment_terms?: string;
  
  // Additional Information
  reference_number?: string;
  memo?: string;
  notes?: string;
  ship_to_address?: string;
  
  // Email Tracking
  email_sent?: boolean;
  email_sent_date?: string;
  acknowledged_date?: string;
  
  // Line Items
  line_items: SalesOrderItem[];
  
  // Metadata
  created_at?: string;
  updated_at?: string;
}

export interface SalesOrderFormData {
  customer: number;
  so_date: string;
  expected_date?: string;
  seller_name?: string;
  seller_email?: string;
  seller_phone?: string;
  status: 'draft' | 'pending' | 'sent' | 'acknowledged';
  payment_terms?: string;
  reference_number?: string;
  memo?: string;
  notes?: string;
  ship_to_address?: string;
  line_items: SalesOrderItem[];
}

export interface SalesOrderFilters {
  status?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface SalesOrderStats {
  total_orders: number;
  total_sales: number;
  pending_amount: number;
  delivered_count: number;
  draft_count: number;
  cancelled_count: number;
}

// Additional interfaces for new backend integration
export interface AvailableProduct {
  id: number;
  product_id: string;
  name: string;
  sku: string;
  product_type: 'product' | 'service' | 'bundle';
  description?: string;
  unit_price: string;
  cost_price: string;
  minimum_selling_price: string;
  status: 'active' | 'inactive';
  category: number;
  category_name: string;
  taxable: boolean;
  sales_tax_category: string;
  track_inventory: boolean;
  quantity_on_hand: number;
  total_quantity_on_hand: number;
}

export interface SalesOrderStats {
  total_orders: number;
  draft_orders: number;
  pending_orders: number;
  delivered_orders: number;
  total_value: number;
  pending_value: number;
}

class SalesOrderService {
  private baseUrl = '/api/sales';

  async getSalesOrders(filters?: SalesOrderFilters): Promise<{ results: SalesOrder[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customer) params.append('customer', filters.customer.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/sales-orders/?${params.toString()}`);
    
    // Map customer details to customer_name for display
    const salesOrders = response.data.results?.map((so: SalesOrder) => ({
      ...so,
      customer_name: so.customer_details?.display_name || `Customer ${so.customer}`
    })) || [];

    return {
      results: salesOrders,
      count: response.data.count || 0
    };
  }

  async getSalesOrder(id: number): Promise<SalesOrder> {
    const response = await api.get(`${this.baseUrl}/sales-orders/${id}/`);
    
    // Map customer details to customer_name for display
    return {
      ...response.data,
      customer_name: response.data.customer_details?.display_name || `Customer ${response.data.customer}`
    };
  }

  async createSalesOrder(data: SalesOrderFormData): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/sales-orders/`, data);
    return response.data;
  }

  async updateSalesOrder(id: number, data: Partial<SalesOrderFormData>): Promise<SalesOrder> {
    const response = await api.patch(`${this.baseUrl}/sales-orders/${id}/`, data);
    return response.data;
  }

  async deleteSalesOrder(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/sales-orders/${id}/`);
  }

  async getSalesOrderStats(): Promise<SalesOrderStats> {
    const response = await api.get(`${this.baseUrl}/sales-orders/stats/`);
    return response.data;
  }

  async duplicateSalesOrder(id: number): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/sales-orders/${id}/duplicate/`);
    return response.data;
  }

  async sendSalesOrder(id: number, emailData?: { to: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/sales-orders/${id}/send/`, emailData);
  }

  async acknowledgeSalesOrder(id: number): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/sales-orders/${id}/acknowledge/`);
    return response.data;
  }

  async markAsDelivered(id: number, deliveryData?: { delivery_date?: string; notes?: string }): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/sales-orders/${id}/mark_delivered/`, deliveryData);
    return response.data;
  }

  async cancelSalesOrder(id: number, reason?: string): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/sales-orders/${id}/cancel/`, { reason });
    return response.data;
  }

  // Get sales orders that can be billed
  async getBillableSalesOrders(): Promise<SalesOrder[]> {
    const response = await api.get(`${this.baseUrl}/sales-orders/billable/`);
    return response.data.results || [];
  }

  // New backend integration methods
  async getDashboardStats(): Promise<SalesOrderStats> {
    const response = await api.get(`${this.baseUrl}/dashboard_stats/`);
    return response.data;
  }

  async getAvailableProducts(): Promise<AvailableProduct[]> {
    console.log('🔍 SalesOrderService.getAvailableProducts() called');
    console.log('🔍 Base URL:', this.baseUrl);
    console.log('🔍 Full URL:', `${this.baseUrl}/products/`);

    const response = await api.get(`${this.baseUrl}/products/`);
    console.log('🔍 API Response:', response.data);

    const products = response.data.results || response.data;
    console.log('🔍 Processed products:', products);

    return products;
  }

  async createDeliveryNote(id: number, data: {
    delivery_date?: string;
    delivery_address?: string;
    notes?: string;
    quantity_delivered?: number;
  }) {
    const response = await api.post(`${this.baseUrl}/${id}/create_delivery_note/`, data);
    return response.data;
  }

  // Status Updates
  async markAsSent(id: number) {
    const response = await api.post(`${this.baseUrl}/${id}/mark_as_sent/`);
    return response.data;
  }

  async markAsAcknowledged(id: number) {
    const response = await api.post(`${this.baseUrl}/${id}/mark_as_acknowledged/`);
    return response.data;
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      draft: '#9e9e9e',
      pending: '#ff9800',
      sent: '#2196f3',
      acknowledged: '#4caf50',
      partial: '#ff5722',
      delivered: '#8bc34a',
      closed: '#607d8b',
      cancelled: '#f44336',
    };
    return statusColors[status] || '#9e9e9e';
  }

  getStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      draft: 'Draft',
      pending: 'Pending',
      sent: 'Sent',
      acknowledged: 'Acknowledged',
      partial: 'Partially Delivered',
      delivered: 'Delivered',
      closed: 'Closed',
      cancelled: 'Cancelled',
    };
    return statusLabels[status] || status;
  }
}

export const salesOrderService = new SalesOrderService();
export default salesOrderService;
