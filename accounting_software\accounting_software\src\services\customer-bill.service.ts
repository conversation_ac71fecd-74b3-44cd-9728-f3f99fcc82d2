import api from './api';

export interface CustomerBillItem {
  id?: number;
  product_id: number | null;
  product_name: string;
  description: string;
  quantity: number;
  unit_price: number;
  discount_percent: number;
  line_total: number;
  sales_tax_id: number | null;
  sales_tax_rate: number;
  sales_tax_amount: number;
  account_id: number | null;
  account_name: string;
}

export interface CustomerBill {
  id?: number;
  bill_number: string;
  customer_id: number;
  customer_name: string;
  receivable_account_id: number | null;
  bill_date: string;
  due_date: string;
  reference_number: string;
  status: 'draft' | 'posted';
  payment_terms_id: number | null;
  payment_terms_name: string;
  notes: string;
  line_items: CustomerBillItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  source_type?: 'manual' | 'sales_order' | 'delivery_note';
  source_document_id?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CreateCustomerBillRequest {
  customer_id: number;
  receivable_account_id: number | null;
  bill_date: string;
  due_date: string;
  reference_number: string;
  status: 'draft' | 'posted';
  payment_terms_id: number | null;
  notes: string;
  line_items: Omit<CustomerBillItem, 'id'>[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  source_type?: 'manual' | 'sales_order' | 'delivery_note';
  source_document_id?: number;
}

class CustomerBillService {
  private baseURL = '/sales/customer-bills';

  async getCustomerBills(): Promise<CustomerBill[]> {
    try {
      const response = await api.get(`${this.baseURL}/`);
      return response.data.results || response.data;
    } catch (error) {
      console.error('Error fetching customer bills:', error);
      throw error;
    }
  }

  async getCustomerBill(id: number): Promise<CustomerBill> {
    try {
      const response = await api.get(`${this.baseURL}/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer bill:', error);
      throw error;
    }
  }

  async createCustomerBill(billData: CreateCustomerBillRequest): Promise<CustomerBill> {
    try {
      // For now, return mock data since backend endpoint doesn't exist yet
      console.log('Creating customer bill with data:', billData);

      const mockBill: CustomerBill = {
        id: Date.now(),
        bill_number: await this.generateBillNumber(),
        customer_id: billData.customer_id,
        customer_name: 'Mock Customer', // Would come from customer lookup
        receivable_account_id: billData.receivable_account_id,
        bill_date: billData.bill_date,
        due_date: billData.due_date,
        reference_number: billData.reference_number,
        status: billData.status,
        payment_terms_id: billData.payment_terms_id,
        payment_terms_name: 'Net 30', // Would come from payment terms lookup
        notes: billData.notes,
        line_items: billData.line_items.map((item, index) => ({
          id: index + 1,
          ...item
        })),
        subtotal: billData.subtotal,
        tax_amount: billData.tax_amount,
        total_amount: billData.total_amount,
        amount_paid: 0,
        balance_due: billData.total_amount,
        source_type: billData.source_type,
        source_document_id: billData.source_document_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('Mock customer bill created:', mockBill);
      return mockBill;

      // TODO: Uncomment when backend endpoint is ready
      // const response = await api.post(`${this.baseURL}/`, billData);
      // return response.data;
    } catch (error) {
      console.error('Error creating customer bill:', error);
      throw error;
    }
  }

  async updateCustomerBill(id: number, billData: Partial<CreateCustomerBillRequest>): Promise<CustomerBill> {
    try {
      const response = await api.patch(`${this.baseURL}/${id}/`, billData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer bill:', error);
      throw error;
    }
  }

  async deleteCustomerBill(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseURL}/${id}/`);
    } catch (error) {
      console.error('Error deleting customer bill:', error);
      throw error;
    }
  }

  async createFromSalesOrder(salesOrderId: number): Promise<CustomerBill> {
    try {
      const response = await api.post(`${this.baseURL}/create-from-sales-order/`, {
        sales_order_id: salesOrderId
      });
      return response.data;
    } catch (error) {
      console.error('Error creating customer bill from sales order:', error);
      throw error;
    }
  }

  async postCustomerBill(id: number): Promise<CustomerBill> {
    try {
      const response = await api.post(`${this.baseURL}/${id}/post/`);
      return response.data;
    } catch (error) {
      console.error('Error posting customer bill:', error);
      throw error;
    }
  }

  async generateBillNumber(): Promise<string> {
    try {
      // For now, use client-side generation since backend endpoint doesn't exist
      const timestamp = Date.now();
      const billNumber = `INV-${timestamp.toString().slice(-6)}`;
      console.log('Generated bill number:', billNumber);
      return billNumber;

      // TODO: Uncomment when backend endpoint is ready
      // const response = await api.get(`${this.baseURL}/generate-bill-number/`);
      // return response.data.bill_number;
    } catch (error) {
      console.error('Error generating bill number:', error);
      // Fallback to client-side generation
      const timestamp = Date.now();
      return `INV-${timestamp.toString().slice(-6)}`;
    }
  }
}

export const customerBillService = new CustomerBillService();
export default customerBillService;
