import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Autocomplete,
  IconButton,
  Box,
  Typography,
  Tooltip,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import FormattedCurrencyInput from './FormattedCurrencyInput';
import { formatCurrency } from '../utils/formatters';
import { loadChartOfAccountsFast } from '../../services/gl.service';
import type { Account } from '../types/gl.types';
import { salesTaxService, type SalesTaxOption } from '../../services/sales-tax.service';

// Customer Bill Line Item interface
export interface CustomerBillLineItem {
  id: string;
  product_id?: number | null;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  discount_percent: number;
  line_total: number;
  sales_tax_id?: number | null;
  sales_tax_rate?: number;
  sales_tax_amount?: number;
  account_id?: number | null;
  account_code?: string;
}

// Product option interface
export interface ProductOption {
  id: number;
  name: string;
  description?: string;
  sales_price?: number;
  revenue_account_code?: string;
}

interface CustomerBillLineTableProps {
  lineItems: CustomerBillLineItem[];
  onLineItemsChange: (lineItems: CustomerBillLineItem[]) => void;
  products: ProductOption[];
  readOnly?: boolean;
  currencySymbol?: string;
}

const CustomerBillLineTable: React.FC<CustomerBillLineTableProps> = ({
  lineItems,
  onLineItemsChange,
  products,
  readOnly = false,
  currencySymbol = '$',
}) => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [salesTaxes, setSalesTaxes] = useState<SalesTaxOption[]>([]);
  const [loadingAccounts, setLoadingAccounts] = useState(true);
  const [loadingSalesTaxes, setLoadingSalesTaxes] = useState(true);

  // Load accounts and sales taxes
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load accounts
        setLoadingAccounts(true);
        const accountsData = await loadChartOfAccountsFast();
        // Filter for revenue accounts (typically 4000-4999)
        const revenueAccounts = accountsData.filter(account => 
          account.account_code.startsWith('4') || 
          account.account_type?.toLowerCase().includes('revenue') ||
          account.account_type?.toLowerCase().includes('income')
        );
        setAccounts(revenueAccounts);
        setLoadingAccounts(false);

        // Load output sales taxes (for customer bills)
        setLoadingSalesTaxes(true);
        const allTaxes = await salesTaxService.getAllSalesTaxes();
        const outputTaxes = allTaxes.filter(tax => tax.tax_type === 'output');
        setSalesTaxes(outputTaxes);
        setLoadingSalesTaxes(false);
      } catch (error) {
        console.error('Error loading data:', error);
        setLoadingAccounts(false);
        setLoadingSalesTaxes(false);
      }
    };

    loadData();
  }, []);

  const handleLineItemChange = (index: number, field: keyof CustomerBillLineItem, value: any) => {
    const updatedLineItems = [...lineItems];
    const lineItem = { ...updatedLineItems[index] };

    if (field === 'product_id') {
      const selectedProduct = products.find(p => p.id === value);
      if (selectedProduct) {
        lineItem.product_id = selectedProduct.id;
        lineItem.product_name = selectedProduct.name;
        lineItem.item_description = selectedProduct.description || selectedProduct.name;
        lineItem.unit_price = selectedProduct.sales_price || 0;
        lineItem.account_code = selectedProduct.revenue_account_code || '';
        // Find matching account
        const matchingAccount = accounts.find(acc => acc.account_code === selectedProduct.revenue_account_code);
        if (matchingAccount) {
          lineItem.account_id = matchingAccount.id;
        }
      }
    } else if (field === 'account_id') {
      const selectedAccount = accounts.find(acc => acc.id === value);
      if (selectedAccount) {
        lineItem.account_id = selectedAccount.id;
        lineItem.account_code = selectedAccount.account_code;
      }
    } else if (field === 'sales_tax_id') {
      const selectedTax = salesTaxes.find(tax => tax.id === value);
      if (selectedTax) {
        lineItem.sales_tax_id = selectedTax.id;
        lineItem.sales_tax_rate = selectedTax.rate;
      } else {
        lineItem.sales_tax_id = null;
        lineItem.sales_tax_rate = 0;
      }
    } else {
      (lineItem as any)[field] = value;
    }

    // Recalculate line total and tax amount
    const subtotal = lineItem.quantity * lineItem.unit_price;
    const discount = subtotal * (lineItem.discount_percent / 100);
    lineItem.line_total = subtotal - discount;
    lineItem.sales_tax_amount = lineItem.line_total * ((lineItem.sales_tax_rate || 0) / 100);

    updatedLineItems[index] = lineItem;
    onLineItemsChange(updatedLineItems);
  };

  const addLineItem = () => {
    const newLineItem: CustomerBillLineItem = {
      id: `line_${Date.now()}`,
      product_id: null,
      product_name: '',
      item_description: '',
      quantity: 1,
      unit_price: 0,
      discount_percent: 0,
      line_total: 0,
      sales_tax_id: null,
      sales_tax_rate: 0,
      sales_tax_amount: 0,
      account_id: null,
      account_code: '',
    };
    onLineItemsChange([...lineItems, newLineItem]);
  };

  const removeLineItem = (index: number) => {
    const updatedLineItems = lineItems.filter((_, i) => i !== index);
    onLineItemsChange(updatedLineItems);
  };

  return (
    <Box>
      <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ minWidth: 200 }}>Product/Service</TableCell>
              <TableCell sx={{ minWidth: 250 }}>Description</TableCell>
              <TableCell sx={{ minWidth: 100 }}>Qty</TableCell>
              <TableCell sx={{ minWidth: 120 }}>Unit Price</TableCell>
              <TableCell sx={{ minWidth: 100 }}>Discount %</TableCell>
              <TableCell sx={{ minWidth: 150 }}>Output Tax</TableCell>
              <TableCell sx={{ minWidth: 150 }}>Revenue Account</TableCell>
              <TableCell sx={{ minWidth: 120 }}>Total</TableCell>
              {!readOnly && <TableCell sx={{ width: 50 }}>Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {lineItems.map((lineItem, index) => (
              <TableRow key={lineItem.id}>
                <TableCell>
                  <Autocomplete
                    size="small"
                    options={products}
                    getOptionLabel={(option) => option.name}
                    value={products.find(p => p.id === lineItem.product_id) || null}
                    onChange={(_, newValue) => handleLineItemChange(index, 'product_id', newValue?.id || null)}
                    disabled={readOnly}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select product..."
                        variant="outlined"
                        size="small"
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={lineItem.item_description}
                    onChange={(e) => handleLineItemChange(index, 'item_description', e.target.value)}
                    disabled={readOnly}
                    placeholder="Enter description..."
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    size="small"
                    value={lineItem.quantity}
                    onChange={(e) => handleLineItemChange(index, 'quantity', Number(e.target.value))}
                    disabled={readOnly}
                    inputProps={{ min: 0, step: 0.01 }}
                    sx={{ width: 80 }}
                  />
                </TableCell>
                <TableCell>
                  <FormattedCurrencyInput
                    size="small"
                    value={lineItem.unit_price}
                    onChange={(e) => handleLineItemChange(index, 'unit_price', Number(e.target.value))}
                    disabled={readOnly}
                    currencySymbol={currencySymbol}
                    sx={{ width: 100 }}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    size="small"
                    value={lineItem.discount_percent}
                    onChange={(e) => handleLineItemChange(index, 'discount_percent', Number(e.target.value))}
                    disabled={readOnly}
                    inputProps={{ min: 0, max: 100, step: 0.1 }}
                    sx={{ width: 80 }}
                  />
                </TableCell>
                <TableCell>
                  <Autocomplete
                    size="small"
                    options={salesTaxes}
                    getOptionLabel={(option) => `${option.description} (${option.rate}%)`}
                    value={salesTaxes.find(tax => tax.id === lineItem.sales_tax_id) || null}
                    onChange={(_, newValue) => handleLineItemChange(index, 'sales_tax_id', newValue?.id || null)}
                    disabled={readOnly || loadingSalesTaxes}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select tax..."
                        variant="outlined"
                        size="small"
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <Autocomplete
                    size="small"
                    options={accounts}
                    getOptionLabel={(option) => `${option.account_code} - ${option.account_name}`}
                    value={accounts.find(acc => acc.id === lineItem.account_id) || null}
                    onChange={(_, newValue) => handleLineItemChange(index, 'account_id', newValue?.id || null)}
                    disabled={readOnly || loadingAccounts}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select account..."
                        variant="outlined"
                        size="small"
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {formatCurrency(lineItem.line_total + (lineItem.sales_tax_amount || 0), currencySymbol)}
                  </Typography>
                </TableCell>
                {!readOnly && (
                  <TableCell>
                    <Tooltip title="Remove line">
                      <IconButton
                        size="small"
                        onClick={() => removeLineItem(index)}
                        disabled={lineItems.length === 1}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {!readOnly && (
        <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-start' }}>
          <Tooltip title="Add line item">
            <IconButton onClick={addLineItem} color="primary">
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )}
    </Box>
  );
};

export default CustomerBillLineTable;
