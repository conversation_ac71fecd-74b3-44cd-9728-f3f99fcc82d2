import React, { useState, useEffect } from 'react';
import {
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Autocomplete,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Paper,
  FormHelperText,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Person as PersonIcon,
  LocalShipping as ShippingIcon,
  Description as DescriptionIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ShoppingCart as ShoppingCartIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { useCustomers } from '../../../contexts/CustomerContext';
import { useProducts } from '../../../contexts/ProductContext';
import { Product } from '../../../shared/types/product.types';
import salesOrderService from '../../../services/salesOrder.service';
import api from '../../../services/api';
import {
  JournalLineTable,
  type JournalLineItem,
  type AccountOption,
  FormattedCurrencyInput,
  QuantityInput,
} from '../../../shared/components';
import { salesTaxService, type SalesTaxOption } from '../../../services/sales-tax.service';

// Enhanced line item interface for SO (same pattern as PO)
interface SOLineItem extends JournalLineItem {
  product_id: string | number | null;
  product_name: string;
  quantity: number;
  unit_of_measure: string;
  unit_price: number;
  discount_percent: number;
  line_total: number;
}

interface SalesOrderFormData {
  customer_id: number | null;
  so_date: string;
  expected_date: string;
  seller_name: string;
  seller_email: string;
  seller_phone: string;
  reference_number: string;
  memo: string;
  notes: string;
  ship_to_address: string;
  payment_terms: string;
  status: 'draft' | 'pending' | 'sent' | 'acknowledged';
  line_items: SOLineItem[];
}

interface SalesOrderFormProps {
  onSubmit: (values: SalesOrderFormData, action: 'save' | 'save-close' | 'save-new') => void;
  salesOrder?: any;
  onClose?: () => void;
}

const validationSchema = Yup.object({
  customer_id: Yup.number().required('Customer is required'),
  so_date: Yup.string().required('Sales order date is required'),
  expected_date: Yup.string().required('Expected date is required'),
  seller_name: Yup.string().required('Seller name is required'),
  seller_email: Yup.string().email('Invalid email format'),
  payment_terms: Yup.string().required('Payment terms are required'),
});

const SalesOrderForm: React.FC<SalesOrderFormProps> = ({
  onSubmit,
  salesOrder,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { products: availableProducts, isLoading: loadingProducts } = useProducts();

  // Units of Measure (same as purchase order)
  const COMMON_UNITS = [
    { id: 'pcs', name: 'Pieces', abbreviation: 'pcs', type: 'count' },
    { id: 'kg', name: 'Kilogram', abbreviation: 'kg', type: 'weight' },
    { id: 'g', name: 'Gram', abbreviation: 'g', type: 'weight' },
    { id: 'l', name: 'Liter', abbreviation: 'L', type: 'volume' },
    { id: 'ml', name: 'Milliliter', abbreviation: 'ml', type: 'volume' },
    { id: 'm', name: 'Meter', abbreviation: 'm', type: 'length' },
    { id: 'cm', name: 'Centimeter', abbreviation: 'cm', type: 'length' },
    { id: 'sqm', name: 'Square Meter', abbreviation: 'sq.m', type: 'area' },
    { id: 'box', name: 'Box', abbreviation: 'box', type: 'count' },
    { id: 'pack', name: 'Pack', abbreviation: 'pack', type: 'count' },
  ];

  // Sales tax state
  const [salesTaxes, setSalesTaxes] = useState<SalesTaxOption[]>([]);
  const [loadingSalesTaxes, setLoadingSalesTaxes] = useState(false);
  const [lineItems, setLineItems] = useState<SOLineItem[]>([]);
  const [customersFromAPI, setCustomersFromAPI] = useState<any[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  const { currencyInfo } = useCurrencyInfo();
  const { customers: customersFromContext } = useCustomers() || { customers: [] };

  // Use customers from context if available, otherwise use API customers
  const customers = customersFromContext.length > 0 ? customersFromContext : customersFromAPI;

  // Create empty line item (same pattern as PO)
  const createEmptyLineItem = (index: number): SOLineItem => ({
    id: `line_${Date.now()}_${index}`,
    product_id: null,
    product_name: '',
    description: '',
    quantity: 1,
    unit_of_measure: 'pcs',
    unit_price: 0,
    discount_percent: 0,
    line_total: 0,
    amount: 0,
    account_id: null,
    account_name: '',
    sales_tax: null,
    sales_tax_description: '',
    sales_tax_rate: 0,
    sales_tax_amount: 0,
    taxable_amount: 0,
  });

  // Load sales taxes
  useEffect(() => {
    const loadSalesTaxes = async () => {
      try {
        setLoadingSalesTaxes(true);
        const allTaxes = await salesTaxService.getSalesTaxes();
        console.log('All sales taxes loaded:', allTaxes);

        // For debugging, use ALL taxes first to see if column appears
        console.log('🔄 Setting sales taxes:', allTaxes);
        setSalesTaxes(allTaxes);
        console.log('✅ Sales taxes set, count:', allTaxes.length);
      } catch (error) {
        console.error('Failed to load sales taxes:', error);
      } finally {
        setLoadingSalesTaxes(false);
      }
    };

    loadSalesTaxes();
  }, []);

  // Initialize with one empty line item
  useEffect(() => {
    if (lineItems.length === 0) {
      setLineItems([createEmptyLineItem(0)]);
    }
  }, []);

  const formik = useFormik<SalesOrderFormData>({
    initialValues: {
      customer_id: salesOrder?.customer_id || null,
      so_date: salesOrder?.so_date || dayjs().format('YYYY-MM-DD'),
      expected_date: salesOrder?.expected_date || dayjs().add(7, 'day').format('YYYY-MM-DD'),
      seller_name: salesOrder?.seller_name || '',
      seller_email: salesOrder?.seller_email || '',
      seller_phone: salesOrder?.seller_phone || '',
      reference_number: salesOrder?.reference_number || '',
      memo: salesOrder?.memo || '',
      notes: salesOrder?.notes || '',
      ship_to_address: salesOrder?.ship_to_address || '',
      payment_terms: salesOrder?.payment_terms || 'Net 30',
      status: salesOrder?.status || 'draft',
      line_items: salesOrder?.line_items || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);
      try {
        await onSubmit(values, 'save');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    },
  });

  // Load customers from API if not available from context
  useEffect(() => {
    const loadCustomers = async () => {
      if (customersFromContext.length > 0) return; // Skip if context has customers

      setLoadingCustomers(true);
      try {
        const response = await api.get('/contacts/customers/', {
          params: { page_size: 1000 }
        });
        const data = response.data.results || response.data;
        console.log('Customers loaded for sales order:', data);
        setCustomersFromAPI(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('Error loading customers:', err);
        setError('Failed to load customers');
      } finally {
        setLoadingCustomers(false);
      }
    };

    loadCustomers();
  }, [customersFromContext.length]);





  // Calculation functions
  const calculateLineTotal = (quantity: number, unitPrice: number, discountPercent: number) => {
    const subtotal = quantity * unitPrice;
    const discount = subtotal * (discountPercent / 100);
    return subtotal - discount;
  };

  const getCurrencySymbol = () => {
    return currencyInfo?.functional_currency === 'USD' ? '$' : '₹';
  };

  const transformProductsToAccountOptions = (): AccountOption[] => {
    console.log('🔄 Transforming products to account options:', availableProducts);
    const options = availableProducts.map(product => {
      const option = {
        id: product.id,
        account_number: product.sku,
        account_name: product.name,
        detail_type: 'Product',
        account_type: 'Product',
      };
      console.log(`✅ Transformed product ${product.name} to option:`, option);
      return option;
    });
    console.log('✅ All transformed options:', options);
    return options;
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setLineItems(prevItems =>
      prevItems.map(item => {
        if (item.id === lineId) {
          const updatedItem = { ...item };

          if (field === 'account_selection') {
            // Handle product selection from account dropdown
            const selectedProduct = availableProducts.find(p => p.id === value?.id);
            if (selectedProduct) {
              updatedItem.product_id = selectedProduct.id;
              updatedItem.product_name = selectedProduct.name;
              updatedItem.description = selectedProduct.name;
              updatedItem.unit_price = selectedProduct.salesPrice || 0;
              updatedItem.unit_of_measure = 'pcs';
              updatedItem.account_id = selectedProduct.id;
              updatedItem.account_name = selectedProduct.name;
              // Recalculate totals with new product
              updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
              if (updatedItem.sales_tax) {
                const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
                if (selectedTax) {
                  updatedItem.taxable_amount = updatedItem.line_total;
                  updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
                }
              }
            }
          } else if (field === 'description') {
            updatedItem.description = value;
          } else if (field === 'unit_of_measure') {
            updatedItem.unit_of_measure = value;
          } else if (field === 'quantity') {
            updatedItem.quantity = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'unit_price') {
            updatedItem.unit_price = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'discount_percent') {
            updatedItem.discount_percent = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'sales_tax') {
            console.log('🔍 Handling sales tax change:', value);
            updatedItem.sales_tax = value;
            const selectedTax = salesTaxes.find(tax => tax.id === value);
            console.log('🔍 Selected tax:', selectedTax);
            if (selectedTax) {
              updatedItem.sales_tax_description = selectedTax.description;
              updatedItem.sales_tax_rate = selectedTax.rate;
              const taxableAmount = updatedItem.line_total;
              updatedItem.taxable_amount = taxableAmount;
              updatedItem.sales_tax_amount = (taxableAmount * selectedTax.rate) / 100;
              console.log('🔍 Tax calculation:', {
                taxableAmount,
                rate: selectedTax.rate,
                taxAmount: updatedItem.sales_tax_amount
              });
            } else {
              updatedItem.sales_tax_description = '';
              updatedItem.sales_tax_rate = 0;
              updatedItem.sales_tax_amount = 0;
              updatedItem.taxable_amount = 0;
            }
          }

          // Always update final amount
          updatedItem.amount = updatedItem.line_total + (updatedItem.sales_tax_amount || 0);
          return updatedItem;
        }
        return item;
      })
    );
  };

  const handleAddLine = () => {
    const newLineItem = createEmptyLineItem(lineItems.length);
    setLineItems(prevItems => [...prevItems, newLineItem]);
  };

  const handleRemoveLine = (lineId: string) => {
    setLineItems(prevItems => prevItems.filter(item => item.id !== lineId));
  };

  const getAdditionalColumns = () => [
    {
      key: 'quantity',
      label: 'Qty',
      width: 120,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const soLine = line as SOLineItem;
        return (
          <QuantityInput
            size="small"
            value={soLine.quantity}
            onChange={(e) => onChange(Number(e.target.value))}
            min={0.01}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'unit_of_measure',
      label: 'Unit',
      width: 80,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const soLine = line as SOLineItem;
        return (
          <FormControl fullWidth size="small">
            <Select
              value={soLine.unit_of_measure}
              onChange={(e) => onChange(e.target.value)}
            >
              {COMMON_UNITS.map((unit, index) => (
                <MenuItem key={`unit-${unit.id || index}`} value={unit.id}>
                  {unit.abbreviation}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      },
    },
    {
      key: 'unit_price',
      label: 'Unit Price',
      width: 150,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const soLine = line as SOLineItem;
        const unitPrice = parseFloat(soLine.unit_price?.toString() || '0');
        return (
          <FormattedCurrencyInput
            size="small"
            name={`unit_price_${soLine.id}`}
            value={unitPrice}
            onChange={(e) => {
              const newValue = parseFloat(e.target.value) || 0;
              onChange(newValue);
            }}
            currencySymbol={getCurrencySymbol()}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'discount_percent',
      label: 'Discount %',
      width: 100,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const soLine = line as SOLineItem;
        return (
          <TextField
            type="number"
            size="small"
            value={soLine.discount_percent}
            onChange={(e) => onChange(Number(e.target.value))}
            inputProps={{ min: 0, max: 100, step: 0.1, style: { textAlign: 'right' } }}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'sales_tax',
      label: 'Output Tax',
      width: 150,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const soLine = line as SOLineItem;
        console.log('🔍 Rendering sales tax column for line:', soLine.id, 'Available taxes:', salesTaxes.length);
        console.log('🔍 Sales taxes array:', salesTaxes);
        return (
          <Autocomplete
            size="small"
            options={salesTaxes}
            getOptionLabel={(option) => option.description}
            value={salesTaxes.find(tax => tax.id === soLine.sales_tax) || null}
            onChange={(_, newValue) => {
              console.log('🔍 Sales tax selected:', newValue?.description);
              onChange(newValue?.id || null);
            }}
            loading={loadingSalesTaxes}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Select Output Tax..."
                size="small"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {loadingSalesTaxes ? <CircularProgress color="inherit" size={16} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'line_total',
      label: 'Total',
      width: 120,
      render: (line: JournalLineItem) => {
        const soLine = line as SOLineItem;
        const lineTotal = parseFloat(soLine.line_total?.toString() || '0');
        const taxAmount = parseFloat(soLine.sales_tax_amount?.toString() || '0');
        const totalWithTax = lineTotal + taxAmount;
        return (
          <Typography variant="body2" fontWeight="medium" sx={{ textAlign: 'right' }}>
            {getCurrencySymbol()}
            {totalWithTax.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Typography>
        );
      },
    },
  ];

  // Calculate totals from lineItems state
  const calculateSubtotal = () => {
    return lineItems.reduce((total, item) => total + item.line_total, 0);
  };

  const calculateTotalTax = () => {
    return lineItems.reduce((total, item) => total + (item.sales_tax_amount || 0), 0);
  };

  const calculateGrandTotal = () => {
    return calculateSubtotal() + calculateTotalTax();
  };

  const subtotal = calculateSubtotal();
  const totalTax = calculateTotalTax();
  const total = calculateGrandTotal();

  const handleSubmitWithAction = async (action: 'save' | 'save-close' | 'save-new') => {
    setLoading(true);
    setError(null);
    try {
      await onSubmit(formik.values, action);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: 'background.paper',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DescriptionIcon />
            <Typography variant="h6">
              {salesOrder ? 'Edit Sales Order' : 'Create Sales Order'}
            </Typography>
          </Box>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Main Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <form onSubmit={formik.handleSubmit}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <BusinessIcon />
                    <Typography variant="h6">Customer Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Autocomplete
                        options={customers}
                        getOptionLabel={(option) => option.display_name || option.name || option.contact_name || `Customer ${option.id}`}
                        getOptionKey={(option) => option.id}
                        value={customers.find(c => c.id === formik.values.customer_id) || null}
                        onChange={(_, newValue) => {
                          formik.setFieldValue('customer_id', newValue?.id || null);
                        }}
                        loading={loadingCustomers}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Customer *"
                            error={formik.touched.customer_id && Boolean(formik.errors.customer_id)}
                            helperText={formik.touched.customer_id && formik.errors.customer_id}
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {loadingCustomers ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Payment Terms *"
                        name="payment_terms"
                        value={formik.values.payment_terms}
                        onChange={formik.handleChange}
                        error={formik.touched.payment_terms && Boolean(formik.errors.payment_terms)}
                        helperText={formik.touched.payment_terms && formik.errors.payment_terms}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Order Details */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <DescriptionIcon />
                    <Typography variant="h6">Order Details</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Sales Order Date *"
                        value={dayjs(formik.values.so_date)}
                        onChange={(newValue) => {
                          formik.setFieldValue('so_date', newValue?.format('YYYY-MM-DD') || '');
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: formik.touched.so_date && Boolean(formik.errors.so_date),
                            helperText: formik.touched.so_date && formik.errors.so_date,
                          },
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Expected Date *"
                        value={dayjs(formik.values.expected_date)}
                        onChange={(newValue) => {
                          formik.setFieldValue('expected_date', newValue?.format('YYYY-MM-DD') || '');
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: formik.touched.expected_date && Boolean(formik.errors.expected_date),
                            helperText: formik.touched.expected_date && formik.errors.expected_date,
                          },
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Reference Number"
                        name="reference_number"
                        value={formik.values.reference_number}
                        onChange={formik.handleChange}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Status</InputLabel>
                        <Select
                          value={formik.values.status}
                          label="Status"
                          name="status"
                          onChange={formik.handleChange}
                        >
                          <MenuItem value="draft">Draft</MenuItem>
                          <MenuItem value="pending">Pending</MenuItem>
                          <MenuItem value="sent">Sent</MenuItem>
                          <MenuItem value="acknowledged">Acknowledged</MenuItem>
                          <MenuItem value="partial">Partially Delivered</MenuItem>
                          <MenuItem value="delivered">Delivered</MenuItem>
                          <MenuItem value="closed">Closed</MenuItem>
                          <MenuItem value="cancelled">Cancelled</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Status</InputLabel>
                        <Select
                          name="status"
                          value={formik.values.status}
                          onChange={formik.handleChange}
                          label="Status"
                        >
                          <MenuItem value="draft">Draft</MenuItem>
                          <MenuItem value="pending">Pending</MenuItem>
                          <MenuItem value="sent">Sent</MenuItem>
                          <MenuItem value="acknowledged">Acknowledged</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Seller Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <PersonIcon />
                    <Typography variant="h6">Seller Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Name *"
                        name="seller_name"
                        value={formik.values.seller_name}
                        onChange={formik.handleChange}
                        error={formik.touched.seller_name && Boolean(formik.errors.seller_name)}
                        helperText={formik.touched.seller_name && formik.errors.seller_name}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Email"
                        name="seller_email"
                        type="email"
                        value={formik.values.seller_email}
                        onChange={formik.handleChange}
                        error={formik.touched.seller_email && Boolean(formik.errors.seller_email)}
                        helperText={formik.touched.seller_email && formik.errors.seller_email}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Phone"
                        name="seller_phone"
                        value={formik.values.seller_phone}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Line Items using JournalLineTable */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShoppingCartIcon />
                      <Typography variant="h6">Line Items ({lineItems.length})</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {availableProducts?.length || 0} products available
                      </Typography>
                      {loadingProducts && (
                        <CircularProgress size={20} sx={{ ml: 2 }} />
                      )}
                    </Box>
                  </Box>

                  <Box mb={2}>
                    <Typography variant="body2" color="text.secondary">
                      Add products and services to this sales order
                    </Typography>
                    {availableProducts.length === 0 && !loadingProducts && (
                      <Alert severity="warning" sx={{ mt: 2 }}>
                        No products available. Please add products in the pricing module first.
                      </Alert>
                    )}
                  </Box>

                  {loadingSalesTaxes || salesTaxes.length === 0 ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                      <CircularProgress />
                      <Typography sx={{ ml: 2 }}>Loading sales taxes...</Typography>
                    </Box>
                  ) : (
                    <>
                      {console.log('🔍 JournalLineTable Props:', {
                        showSalesTaxColumn: true,
                        salesTaxes: salesTaxes,
                        salesTaxesLength: salesTaxes.length,
                        loadingSalesTaxes: loadingSalesTaxes
                      })}
                      <JournalLineTable
                    tableMode="custom"
                    showAccountColumn
                    showDescriptionColumn
                    showMemoColumn={false}
                    showAmountColumn={false}
                    showDebitCreditColumns={false}
                    showSalesTaxColumn={false}
                    showActionsColumn={true}
                    lines={lineItems}
                    accounts={transformProductsToAccountOptions()}
                    salesTaxes={salesTaxes}
                    onLineChange={handleLineChange}
                    onAddLine={handleAddLine}
                    onRemoveLine={handleRemoveLine}
                    currencySymbol={getCurrencySymbol()}
                    accountColumnLabel="Product"
                    accountPlaceholder="Select Product"
                    descriptionPlaceholder="Enter description"
                    salesTaxColumnLabel="Output Tax"
                    salesTaxPlaceholder="Select Output Tax"
                    additionalColumns={getAdditionalColumns()}
                    minLines={1}
                    tableHeight="300px"
                    readOnly={false}
                    loading={loadingProducts || loadingSalesTaxes}
                  />
                  {/* Debug info */}
                  <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', fontSize: '0.8rem' }}>
                    <Typography variant="caption">
                      Debug: Sales Taxes Count: {salesTaxes.length}, Loading: {loadingSalesTaxes ? 'Yes' : 'No'}
                    </Typography>
                    {salesTaxes.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          Available Sales Taxes:
                        </Typography>
                        {salesTaxes.map((tax, index) => (
                          <Typography key={tax.id} variant="caption" sx={{ display: 'block', ml: 1 }}>
                            {index + 1}. {tax.description} ({tax.rate}%)
                          </Typography>
                        ))}
                      </Box>
                    )}
                  </Box>
                  {/* Validation Message */}
                  {lineItems.length === 0 || !lineItems.some(item => item.description.trim() !== '' || item.quantity > 0 || item.unit_price > 0) ? (
                    <FormHelperText error sx={{ mt: 1 }}>
                      Please add at least one line item with description, quantity, or price
                    </FormHelperText>
                  ) : null}
                    </>
                  )}



                  {/* Totals Summary */}
                  {lineItems.length > 0 && (
                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                      <Box sx={{ minWidth: 250 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                          <Typography>Subtotal:</Typography>
                          <Typography>{currencyInfo?.functional_currency_symbol || '$'}{subtotal.toFixed(2)}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                          <Typography>Tax:</Typography>
                          <Typography>{currencyInfo?.functional_currency_symbol || '$'}{totalTax.toFixed(2)}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5, borderTop: 1, borderColor: 'divider' }}>
                          <Typography fontWeight="bold">Total:</Typography>
                          <Typography fontWeight="bold">{currencyInfo?.functional_currency_symbol || '$'}{total.toFixed(2)}</Typography>
                        </Box>
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <ShippingIcon />
                    <Typography variant="h6">Additional Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Memo"
                        name="memo"
                        multiline
                        rows={3}
                        value={formik.values.memo}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Notes to Customer"
                        name="notes"
                        multiline
                        rows={3}
                        value={formik.values.notes}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Ship To Address"
                        name="ship_to_address"
                        multiline
                        rows={2}
                        value={formik.values.ship_to_address}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          </form>
        </Box>

        {/* Footer Action Bar */}
        <Box sx={{
          p: 2,
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: 'background.paper',
          display: 'flex',
          gap: 1,
          alignItems: 'center'
        }}>
          {onClose && (
            <Button onClick={onClose} disabled={loading}>
              Cancel
            </Button>
          )}

          <Box sx={{ flex: 1 }} />

          <Button
            onClick={() => handleSubmitWithAction('save')}
            disabled={loading}
            variant="outlined"
          >
            {loading ? <CircularProgress size={20} /> : 'Save'}
          </Button>

          <Button
            onClick={() => handleSubmitWithAction('save-new')}
            disabled={loading}
            variant="outlined"
          >
            Save & New
          </Button>

          <Button
            onClick={() => handleSubmitWithAction('save-close')}
            disabled={loading}
            variant="contained"
          >
            Save & Close
          </Button>
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default SalesOrderForm;
