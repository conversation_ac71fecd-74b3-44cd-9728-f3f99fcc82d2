import React, { useState, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Autocomplete,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Person as PersonIcon,
  LocalShipping as ShippingIcon,
  Description as DescriptionIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { useCustomers } from '../../../contexts/CustomerContext';
import { useProducts } from '../../../contexts/ProductContext';
import { Product } from '../../../shared/types/product.types';
import salesOrderService from '../../../services/salesOrder.service';
import api from '../../../services/api';

// Types
interface SalesOrderLineItem {
  id?: number;
  product_id: number | null;
  product_name?: string;
  description: string;
  quantity: number;
  unit_price: number;
  discount_percent: number;
  line_total: number;
  tax_rate: number;
  tax_amount: number;
}

interface SalesOrderFormData {
  customer_id: number | null;
  so_date: string;
  expected_date: string;
  seller_name: string;
  seller_email: string;
  seller_phone: string;
  reference_number: string;
  memo: string;
  notes: string;
  ship_to_address: string;
  payment_terms: string;
  status: 'draft' | 'pending' | 'sent' | 'acknowledged';
  line_items: SalesOrderLineItem[];
}

interface SalesOrderFormProps {
  onSubmit: (values: SalesOrderFormData, action: 'save' | 'save-close' | 'save-new') => void;
  salesOrder?: any;
  onClose?: () => void;
}

const validationSchema = Yup.object({
  customer_id: Yup.number().required('Customer is required'),
  so_date: Yup.string().required('Sales order date is required'),
  expected_date: Yup.string().required('Expected date is required'),
  seller_name: Yup.string().required('Seller name is required'),
  seller_email: Yup.string().email('Invalid email format'),
  payment_terms: Yup.string().required('Payment terms are required'),
});

const SalesOrderForm: React.FC<SalesOrderFormProps> = ({
  onSubmit,
  salesOrder,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { products: availableProducts, isLoading: loadingProducts } = useProducts();

  // Debug products from context
  useEffect(() => {
    console.log('🔍 SalesOrderForm: Products from context:', {
      products: availableProducts,
      count: availableProducts?.length,
      loading: loadingProducts
    });
  }, [availableProducts, loadingProducts]);
  const [customersFromAPI, setCustomersFromAPI] = useState<any[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  const { currencyInfo } = useCurrencyInfo();
  const { customers: customersFromContext } = useCustomers() || { customers: [] };

  // Use customers from context if available, otherwise use API customers
  const customers = customersFromContext.length > 0 ? customersFromContext : customersFromAPI;

  const formik = useFormik<SalesOrderFormData>({
    initialValues: {
      customer_id: salesOrder?.customer_id || null,
      so_date: salesOrder?.so_date || dayjs().format('YYYY-MM-DD'),
      expected_date: salesOrder?.expected_date || dayjs().add(7, 'day').format('YYYY-MM-DD'),
      seller_name: salesOrder?.seller_name || '',
      seller_email: salesOrder?.seller_email || '',
      seller_phone: salesOrder?.seller_phone || '',
      reference_number: salesOrder?.reference_number || '',
      memo: salesOrder?.memo || '',
      notes: salesOrder?.notes || '',
      ship_to_address: salesOrder?.ship_to_address || '',
      payment_terms: salesOrder?.payment_terms || 'Net 30',
      status: salesOrder?.status || 'draft',
      line_items: salesOrder?.line_items || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);
      try {
        await onSubmit(values, 'save');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    },
  });

  // Load customers from API if not available from context
  useEffect(() => {
    const loadCustomers = async () => {
      if (customersFromContext.length > 0) return; // Skip if context has customers

      setLoadingCustomers(true);
      try {
        const response = await api.get('/contacts/customers/', {
          params: { page_size: 1000 }
        });
        const data = response.data.results || response.data;
        console.log('Customers loaded for sales order:', data);
        setCustomersFromAPI(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('Error loading customers:', err);
        setError('Failed to load customers');
      } finally {
        setLoadingCustomers(false);
      }
    };

    loadCustomers();
  }, [customersFromContext.length]);



  // Line Items Management Functions
  const addLineItem = () => {
    const newLineItem: SalesOrderLineItem = {
      product_id: null,
      product_name: '',
      description: '',
      quantity: 1,
      unit_price: 0,
      discount_percent: 0,
      line_total: 0,
      tax_rate: 0,
      tax_amount: 0,
    };

    formik.setFieldValue('line_items', [...formik.values.line_items, newLineItem]);
  };

  const removeLineItem = (index: number) => {
    const updatedItems = formik.values.line_items.filter((_, i) => i !== index);
    formik.setFieldValue('line_items', updatedItems);
  };

  const updateLineItem = (index: number, field: keyof SalesOrderLineItem, value: any) => {
    const updatedItems = [...formik.values.line_items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Recalculate line total when quantity, unit_price, or discount changes
    if (field === 'quantity' || field === 'unit_price' || field === 'discount_percent') {
      const item = updatedItems[index];
      const subtotal = item.quantity * item.unit_price;
      const discountAmount = subtotal * (item.discount_percent / 100);
      const lineTotal = subtotal - discountAmount;
      const taxAmount = lineTotal * (item.tax_rate / 100);

      updatedItems[index].line_total = lineTotal;
      updatedItems[index].tax_amount = taxAmount;
    }

    formik.setFieldValue('line_items', updatedItems);
  };

  const selectProduct = (index: number, product: Product | null) => {
    if (product) {
      updateLineItem(index, 'product_id', product.id);
      updateLineItem(index, 'product_name', product.name);
      updateLineItem(index, 'description', product.description || product.name);
      updateLineItem(index, 'unit_price', product.salesPrice || 0);

      console.log('Product selected:', {
        name: product.name,
        sku: product.sku,
        salesPrice: product.salesPrice
      });
    } else {
      updateLineItem(index, 'product_id', null);
      updateLineItem(index, 'product_name', '');
      updateLineItem(index, 'description', '');
      updateLineItem(index, 'unit_price', 0);
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    const lineItems = formik.values.line_items;
    const subtotal = lineItems.reduce((sum, item) => sum + item.line_total, 0);
    const totalTax = lineItems.reduce((sum, item) => sum + item.tax_amount, 0);
    const total = subtotal + totalTax;

    return { subtotal, totalTax, total };
  };

  const { subtotal, totalTax, total } = calculateTotals();

  const handleSubmitWithAction = async (action: 'save' | 'save-close' | 'save-new') => {
    setLoading(true);
    setError(null);
    try {
      await onSubmit(formik.values, action);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <form onSubmit={formik.handleSubmit}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DescriptionIcon />
            <Typography variant="h6">
              {salesOrder ? 'Edit Sales Order' : 'Create Sales Order'}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent dividers sx={{ minHeight: '500px' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <BusinessIcon />
                    <Typography variant="h6">Customer Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Autocomplete
                        options={customers}
                        getOptionLabel={(option) => option.display_name || option.name || option.contact_name || `Customer ${option.id}`}
                        getOptionKey={(option) => option.id}
                        value={customers.find(c => c.id === formik.values.customer_id) || null}
                        onChange={(_, newValue) => {
                          formik.setFieldValue('customer_id', newValue?.id || null);
                        }}
                        loading={loadingCustomers}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Customer *"
                            error={formik.touched.customer_id && Boolean(formik.errors.customer_id)}
                            helperText={formik.touched.customer_id && formik.errors.customer_id}
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {loadingCustomers ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Payment Terms *"
                        name="payment_terms"
                        value={formik.values.payment_terms}
                        onChange={formik.handleChange}
                        error={formik.touched.payment_terms && Boolean(formik.errors.payment_terms)}
                        helperText={formik.touched.payment_terms && formik.errors.payment_terms}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Order Details */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <DescriptionIcon />
                    <Typography variant="h6">Order Details</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Sales Order Date *"
                        value={dayjs(formik.values.so_date)}
                        onChange={(newValue) => {
                          formik.setFieldValue('so_date', newValue?.format('YYYY-MM-DD') || '');
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: formik.touched.so_date && Boolean(formik.errors.so_date),
                            helperText: formik.touched.so_date && formik.errors.so_date,
                          },
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Expected Date *"
                        value={dayjs(formik.values.expected_date)}
                        onChange={(newValue) => {
                          formik.setFieldValue('expected_date', newValue?.format('YYYY-MM-DD') || '');
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: formik.touched.expected_date && Boolean(formik.errors.expected_date),
                            helperText: formik.touched.expected_date && formik.errors.expected_date,
                          },
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Reference Number"
                        name="reference_number"
                        value={formik.values.reference_number}
                        onChange={formik.handleChange}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Status</InputLabel>
                        <Select
                          value={formik.values.status}
                          label="Status"
                          name="status"
                          onChange={formik.handleChange}
                        >
                          <MenuItem value="draft">Draft</MenuItem>
                          <MenuItem value="pending">Pending</MenuItem>
                          <MenuItem value="sent">Sent</MenuItem>
                          <MenuItem value="acknowledged">Acknowledged</MenuItem>
                          <MenuItem value="partial">Partially Delivered</MenuItem>
                          <MenuItem value="delivered">Delivered</MenuItem>
                          <MenuItem value="closed">Closed</MenuItem>
                          <MenuItem value="cancelled">Cancelled</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Status</InputLabel>
                        <Select
                          name="status"
                          value={formik.values.status}
                          onChange={formik.handleChange}
                          label="Status"
                        >
                          <MenuItem value="draft">Draft</MenuItem>
                          <MenuItem value="pending">Pending</MenuItem>
                          <MenuItem value="sent">Sent</MenuItem>
                          <MenuItem value="acknowledged">Acknowledged</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Seller Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <PersonIcon />
                    <Typography variant="h6">Seller Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Name *"
                        name="seller_name"
                        value={formik.values.seller_name}
                        onChange={formik.handleChange}
                        error={formik.touched.seller_name && Boolean(formik.errors.seller_name)}
                        helperText={formik.touched.seller_name && formik.errors.seller_name}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Email"
                        name="seller_email"
                        type="email"
                        value={formik.values.seller_email}
                        onChange={formik.handleChange}
                        error={formik.touched.seller_email && Boolean(formik.errors.seller_email)}
                        helperText={formik.touched.seller_email && formik.errors.seller_email}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Seller Phone"
                        name="seller_phone"
                        value={formik.values.seller_phone}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Line Items */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShoppingCartIcon />
                      <Typography variant="h6">Line Items</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {availableProducts?.length || 0} products available
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={addLineItem}
                        size="small"
                      >
                        Add Item
                      </Button>
                    </Box>
                  </Box>

                  {formik.values.line_items.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                      <Typography>No line items added yet. Click "Add Item" to get started.</Typography>
                    </Box>
                  ) : (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Product</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell width="100">Qty</TableCell>
                            <TableCell width="120">Unit Price</TableCell>
                            <TableCell width="100">Discount %</TableCell>
                            <TableCell width="120">Line Total</TableCell>
                            <TableCell width="60">Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {formik.values.line_items.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Autocomplete
                                  size="small"
                                  options={availableProducts || []}
                                  getOptionLabel={(option) => `${option.name} (${option.sku}) - $${option.salesPrice}`}
                                  value={availableProducts?.find(p => p.id === item.product_id) || null}
                                  onChange={(_, newValue) => selectProduct(index, newValue)}
                                  loading={loadingProducts}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      placeholder="Select product..."
                                      size="small"
                                      InputProps={{
                                        ...params.InputProps,
                                        endAdornment: (
                                          <>
                                            {loadingProducts ? <CircularProgress color="inherit" size={16} /> : null}
                                            {params.InputProps.endAdornment}
                                          </>
                                        ),
                                      }}
                                    />
                                  )}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  fullWidth
                                  value={item.description}
                                  onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                                  placeholder="Item description..."
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => updateLineItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.unit_price}
                                  onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.discount_percent}
                                  onChange={(e) => updateLineItem(index, 'discount_percent', parseFloat(e.target.value) || 0)}
                                  inputProps={{ min: 0, max: 100, step: 0.01 }}
                                />
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2" fontWeight="medium">
                                  {currencyInfo?.functional_currency_symbol || '$'}{item.line_total.toFixed(2)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Tooltip title="Remove item">
                                  <IconButton
                                    size="small"
                                    onClick={() => removeLineItem(index)}
                                    color="error"
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}

                  {/* Totals Summary */}
                  {formik.values.line_items.length > 0 && (
                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                      <Box sx={{ minWidth: 250 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                          <Typography>Subtotal:</Typography>
                          <Typography>{currencyInfo?.functional_currency_symbol || '$'}{subtotal.toFixed(2)}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                          <Typography>Tax:</Typography>
                          <Typography>{currencyInfo?.functional_currency_symbol || '$'}{totalTax.toFixed(2)}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5, borderTop: 1, borderColor: 'divider' }}>
                          <Typography fontWeight="bold">Total:</Typography>
                          <Typography fontWeight="bold">{currencyInfo?.functional_currency_symbol || '$'}{total.toFixed(2)}</Typography>
                        </Box>
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <ShippingIcon />
                    <Typography variant="h6">Additional Information</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Memo"
                        name="memo"
                        multiline
                        rows={3}
                        value={formik.values.memo}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Notes to Customer"
                        name="notes"
                        multiline
                        rows={3}
                        value={formik.values.notes}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Ship To Address"
                        name="ship_to_address"
                        multiline
                        rows={2}
                        value={formik.values.ship_to_address}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 2, gap: 1 }}>
          {onClose && (
            <Button onClick={onClose} disabled={loading}>
              Cancel
            </Button>
          )}
          
          <Box sx={{ flex: 1 }} />
          
          <Button
            onClick={() => handleSubmitWithAction('save')}
            disabled={loading}
            variant="outlined"
          >
            {loading ? <CircularProgress size={20} /> : 'Save'}
          </Button>
          
          <Button
            onClick={() => handleSubmitWithAction('save-new')}
            disabled={loading}
            variant="outlined"
          >
            Save & New
          </Button>
          
          <Button
            onClick={() => handleSubmitWithAction('save-close')}
            disabled={loading}
            variant="contained"
          >
            Save & Close
          </Button>
        </DialogActions>
      </form>
    </LocalizationProvider>
  );
};

export default SalesOrderForm;
