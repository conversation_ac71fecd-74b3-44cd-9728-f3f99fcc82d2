import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  CircularProgress,
  Dialog,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
  GetApp as GetAppIcon,
  Receipt as ReceiptIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { PageContainer } from '../../../layouts/components/PageComponents';
import SalesOrderForm from '../components/SalesOrderForm';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
// import salesOrderService from '../../../services/salesOrder.service';
import api from '../../../services/api';

// Mock SalesOrder interface
interface SalesOrder {
  id: number;
  so_number: string;
  customer_name: string;
  so_date: string;
  expected_date: string;
  status: 'draft' | 'pending' | 'sent' | 'acknowledged' | 'partial' | 'delivered' | 'closed' | 'cancelled';
  total_amount: number;
  seller_name: string;
  reference_number?: string;
}

const SalesOrdersPage: React.FC = () => {
  const { currencyInfo } = useCurrencyInfo();
  const [salesOrders, setSalesOrders] = useState<SalesOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [soFormOpen, setSoFormOpen] = useState(false);
  const [selectedSO, setSelectedSO] = useState<any>(null);

  // Format currency with proper IFRS compliance and comma separators
  const formatCurrency = (amount: number) => {
    if (!currencyInfo) return '0.00';
    
    const symbol = currencyInfo.functional_currency_symbol;
    
    // Use proper international number formatting with commas
    const formatted = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
    
    return `${symbol}${formatted}`;
  };

  // Mock data for now
  const mockSalesOrders: SalesOrder[] = [
    {
      id: 1,
      so_number: 'SO-000001',
      customer_name: 'ABC Corporation',
      so_date: '2024-03-15',
      expected_date: '2024-03-22',
      status: 'acknowledged',
      total_amount: 2250.00,
      seller_name: 'John Smith',
      reference_number: 'REF-001',
    },
    {
      id: 2,
      so_number: 'SO-000002',
      customer_name: 'XYZ Industries',
      so_date: '2024-03-12',
      expected_date: '2024-03-19',
      status: 'pending',
      total_amount: 4450.75,
      seller_name: 'Jane Doe',
      reference_number: 'REF-002',
    },
    {
      id: 3,
      so_number: 'SO-000003',
      customer_name: 'Tech Solutions Ltd',
      so_date: '2024-03-10',
      expected_date: '2024-03-17',
      status: 'delivered',
      total_amount: 6200.00,
      seller_name: 'Mike Johnson',
      reference_number: 'REF-003',
    },
  ];

  // Load sales orders
  const loadSalesOrders = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch sales orders from API
      const response = await api.get('/sales/sales-orders/', {
        params: { page_size: 1000 } // Get all sales orders
      });

      // Handle Django REST framework pagination
      const data = response.data.results || response.data;
      console.log('Sales Orders API Response:', data);

      if (Array.isArray(data)) {
        setSalesOrders(data);
      } else {
        console.warn('Sales orders data is not an array:', data);
        setSalesOrders([]);
      }
    } catch (err) {
      console.error('Failed to load sales orders:', err);
      setError(err instanceof Error ? err.message : 'Failed to load sales orders');
      // Fallback to mock data if API fails
      setSalesOrders(mockSalesOrders);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSalesOrders();
  }, [loadSalesOrders]);

  // Filter sales orders based on search term
  const filteredSalesOrders = salesOrders.filter(
    (so) =>
      so.so_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      so.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      so.seller_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'default';
      case 'pending':
        return 'warning';
      case 'sent':
        return 'info';
      case 'acknowledged':
        return 'primary';
      case 'partial':
        return 'secondary';
      case 'delivered':
        return 'success';
      case 'closed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Status transition functions
  const getNextStatus = (currentStatus: string): string | null => {
    const statusFlow = {
      'draft': 'pending',
      'pending': 'sent',
      'sent': 'acknowledged',
      'acknowledged': 'delivered',
      'delivered': 'closed'
    };
    return statusFlow[currentStatus as keyof typeof statusFlow] || null;
  };

  const getStatusLabel = (status: string): string => {
    const labels = {
      'draft': 'Draft',
      'pending': 'Pending',
      'sent': 'Sent',
      'acknowledged': 'Acknowledged',
      'partial': 'Partially Delivered',
      'delivered': 'Delivered',
      'closed': 'Closed',
      'cancelled': 'Cancelled'
    };
    return labels[status as keyof typeof labels] || status.toUpperCase();
  };

  const canTransitionStatus = (status: string): boolean => {
    return ['draft', 'pending', 'sent', 'acknowledged', 'delivered'].includes(status);
  };

  const handleStatusChange = async (salesOrder: SalesOrder, newStatus: string) => {
    try {
      setError(null);

      // Update status via API
      await api.patch(`/sales/sales-orders/${salesOrder.id}/`, { status: newStatus });

      setSuccessMessage(`Sales order ${salesOrder.so_number} status updated to ${getStatusLabel(newStatus)}`);

      // Reload data
      await loadSalesOrders();
    } catch (err) {
      console.error('Failed to update status:', err);
      setError(err instanceof Error ? err.message : 'Failed to update sales order status');
    }
  };

  // Handle form submission
  const handleFormSubmit = async (values: any, action: 'save' | 'save-close' | 'save-new') => {
    try {
      setError(null);
      console.log('Sales Order Form Data:', values);

      // Calculate totals from line items
      const lineItems = values.line_items || [];
      const subtotal = lineItems.reduce((sum: number, item: any) => sum + (item.line_total || 0), 0);
      const taxAmount = lineItems.reduce((sum: number, item: any) => sum + (item.tax_amount || 0), 0);
      const totalAmount = subtotal + taxAmount;

      // Prepare data for API
      const salesOrderData = {
        customer: values.customer_id,
        so_date: values.so_date,
        expected_date: values.expected_date,
        seller_name: values.seller_name,
        seller_email: values.seller_email,
        seller_phone: values.seller_phone,
        reference_number: values.reference_number,
        memo: values.memo,
        notes: values.notes,
        ship_to_address: values.ship_to_address,
        payment_terms: values.payment_terms,
        status: values.status || 'draft',
        subtotal: subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        discount_percent: 0,
        discount_amount: 0,
        amount_delivered: 0,
        balance_due: totalAmount
      };

      let result;
      if (selectedSO) {
        // Update existing sales order
        result = await api.put(`/sales/sales-orders/${selectedSO.id}/`, salesOrderData);

        // TODO: Update line items for existing sales order
        // This would require deleting existing line items and creating new ones
        // or implementing a more sophisticated update mechanism
      } else {
        // Create new sales order
        result = await api.post('/sales/sales-orders/', salesOrderData);

        // Create line items if any
        if (lineItems.length > 0 && result.data.id) {
          console.log('Creating line items for sales order:', result.data.id);

          for (const item of lineItems) {
            try {
              const lineItemData = {
                sales_order: result.data.id,
                product: item.product_id,
                description: item.description,
                quantity: item.quantity,
                unit_price: item.unit_price,
                discount_percent: item.discount_percent,
                line_total: item.line_total,
                tax_rate: item.tax_rate,
                tax_amount: item.tax_amount,
              };

              await api.post('/sales/sales-order-line-items/', lineItemData);
            } catch (lineItemError) {
              console.error('Failed to create line item:', lineItemError);
              // Continue with other line items even if one fails
            }
          }
        }
      }

      console.log('Sales Order API Result:', result.data);
      setSuccessMessage(`Sales order ${selectedSO ? 'updated' : 'created'} successfully!`);

      if (action === 'save-close') {
        setSoFormOpen(false);
        setSelectedSO(null);
      } else if (action === 'save-new') {
        setSelectedSO(null);
      }

      // Reload data
      await loadSalesOrders();
    } catch (err) {
      console.error('Failed to save sales order:', err);
      setError(err instanceof Error ? err.message : 'Failed to save sales order');
    }
  };

  // Handle pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <PageContainer title="Sales Orders">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Sales Orders
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Manage customer sales orders and delivery tracking
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setSelectedSO(null);
              setSoFormOpen(true);
            }}
            sx={{ borderRadius: '8px' }}
          >
            Create Sales Order
          </Button>
        </Box>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        )}

        {/* Search and Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by SO number, customer, or seller..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Button startIcon={<FilterListIcon />} variant="outlined">
                  Filters
                </Button>
                <Button startIcon={<GetAppIcon />} variant="outlined">
                  Export
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Sales Orders Table */}
        <Paper sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>SO Number</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Customer</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Expected Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Seller</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600, color: '#495057' }}>Amount</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600, color: '#495057' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : filteredSalesOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <Typography color="text.secondary">
                        No sales orders found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSalesOrders
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((so) => (
                      <TableRow key={so.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {so.so_number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {so.customer_name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(so.so_date).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(so.expected_date).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={getStatusLabel(so.status)}
                              color={getStatusColor(so.status) as any}
                              size="small"
                              sx={{ borderRadius: '6px' }}
                            />
                            {canTransitionStatus(so.status) && (
                              <Tooltip title={`Mark as ${getStatusLabel(getNextStatus(so.status) || '')}`}>
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    const nextStatus = getNextStatus(so.status);
                                    if (nextStatus) {
                                      handleStatusChange(so, nextStatus);
                                    }
                                  }}
                                  sx={{ ml: 0.5 }}
                                >
                                  <SendIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {so.seller_name}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(so.total_amount)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="View">
                              <IconButton size="small">
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSelectedSO(so);
                                  setSoFormOpen(true);
                                }}
                                disabled={so.status === 'closed' || so.status === 'cancelled'}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            {so.status === 'draft' && (
                              <Tooltip title="Send to Customer">
                                <IconButton
                                  size="small"
                                  onClick={() => handleStatusChange(so, 'sent')}
                                  color="primary"
                                >
                                  <SendIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredSalesOrders.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>

        {/* Sales Order Form Dialog */}
        <Dialog
          open={soFormOpen}
          onClose={() => {
            setSoFormOpen(false);
            setSelectedSO(null);
          }}
          fullScreen
        >
          <SalesOrderForm
            onSubmit={handleFormSubmit}
            salesOrder={selectedSO}
            onClose={() => {
              setSoFormOpen(false);
              setSelectedSO(null);
            }}
          />
        </Dialog>
      </Box>
    </PageContainer>
  );
};

export default SalesOrdersPage;
