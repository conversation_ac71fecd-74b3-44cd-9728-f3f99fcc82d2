import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Autocomplete,
  IconButton,
  Divider,
  Paper,
  Alert,
  Snackbar,
  MenuItem,
  Chip,
  Avatar,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Print as PrintIcon,
  Cancel as CancelIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { StandardDatePicker } from '../../../shared/components';
import CustomerBillLineTable, { CustomerBillLineItem, ProductOption } from '../../../shared/components/CustomerBillLineTable';
import { formatCurrency } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { useCustomers, type Customer } from '../../../contexts/CustomerContext';
import { customerBillService, type CustomerBill, type CustomerBillItem } from '../../../services/customer-bill.service';
import { loadChartOfAccountsFast } from '../../../services/gl.service';
import api from '../../../services/api';
import { usePaymentTerms, type PaymentTerm } from '../../../contexts/PaymentTermsContext';
import type { Account } from '../../../shared/types/gl.types';

interface CustomerBillFormData {
  customer_id: number | null;
  receivable_account_id: number | null;
  bill_date: string;
  due_date: string;
  reference_number: string;
  status: 'draft' | 'posted';
  payment_terms_id: number | null;
  payment_terms_name: string;
  notes: string;
  line_items: CustomerBillLineItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  source_type?: 'manual' | 'sales_order' | 'delivery_note';
  source_document_id?: number;
}

interface CreateCustomerBillPageProps {
  customerBill?: CustomerBill | null;
  viewMode?: boolean;
  onClose?: () => void;
  salesOrderId?: number; // For creating from sales order
  salesOrder?: any; // Sales order data
}

const CreateCustomerBillPage: React.FC<CreateCustomerBillPageProps> = ({
  customerBill,
  viewMode = false,
  onClose,
  salesOrderId,
  salesOrder,
}) => {
  const navigate = useNavigate();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  const { customers, loading: customersLoading } = useCustomers();
  const { paymentTerms, loading: paymentTermsLoading } = usePaymentTerms();

  // State
  const [loading, setLoading] = useState(false);
  const [accountsLoading, setAccountsLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [billNumber, setBillNumber] = useState<string>('');

  // Form data
  const [formData, setFormData] = useState<CustomerBillFormData>({
    customer_id: customerBill?.customer_id || salesOrder?.customer_id || null,
    receivable_account_id: customerBill?.receivable_account_id || null,
    bill_date: customerBill?.bill_date || dayjs().format('YYYY-MM-DD'),
    due_date: customerBill?.due_date || dayjs().add(30, 'day').format('YYYY-MM-DD'),
    reference_number: customerBill?.reference_number || salesOrder?.so_number || '',
    status: customerBill?.status || 'draft',
    payment_terms_id: customerBill?.payment_terms_id || null,
    payment_terms_name: customerBill?.payment_terms_name || salesOrder?.payment_terms || '',
    notes: customerBill?.notes || salesOrder?.notes || '',
    line_items: [],
    subtotal: customerBill?.subtotal || 0,
    tax_amount: customerBill?.tax_amount || 0,
    total_amount: customerBill?.total_amount || 0,
    source_type: customerBill?.source_type || (salesOrderId ? 'sales_order' : 'manual'),
    source_document_id: customerBill?.source_document_id || salesOrderId,
  });

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load accounts (receivable accounts)
        setAccountsLoading(true);
        const accountsData = await loadChartOfAccountsFast();
        const receivableAccounts = accountsData.filter(account => 
          account.account_code.startsWith('1') && 
          (account.account_type?.toLowerCase().includes('receivable') ||
           account.account_name.toLowerCase().includes('receivable'))
        );
        setAccounts(receivableAccounts);
        setAccountsLoading(false);

        // Load products from pricing module
        setProductsLoading(true);
        const response = await api.get('/pricing/price-lists/');
        const priceListData = response.data.results || response.data;
        
        if (priceListData.length > 0) {
          const productsData = priceListData.flatMap((priceList: any) => 
            (priceList.items || []).map((item: any) => ({
              id: item.product_id || item.id,
              name: item.product_name || item.name,
              description: item.description || '',
              sales_price: item.sale_price || item.price || 0,
              revenue_account_code: '4000', // Default revenue account
            }))
          );
          setProducts(productsData);
        }
        setProductsLoading(false);

        // Generate bill number if creating new
        if (!customerBill) {
          const newBillNumber = await customerBillService.generateBillNumber();
          setBillNumber(newBillNumber);
        } else {
          setBillNumber(customerBill.bill_number);
        }

      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load required data');
        setAccountsLoading(false);
        setProductsLoading(false);
      }
    };

    loadData();
  }, [customerBill]);

  // Load existing line items
  useEffect(() => {
    if (customerBill && customerBill.line_items) {
      // Load from existing customer bill
      const lineItems = customerBill.line_items.map((item, index) => ({
        id: `line_${index}`,
        product_id: item.product_id,
        product_name: item.product_name,
        item_description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        discount_percent: item.discount_percent,
        line_total: item.line_total,
        sales_tax_id: item.sales_tax_id,
        sales_tax_rate: item.sales_tax_rate,
        sales_tax_amount: item.sales_tax_amount,
        account_id: item.account_id,
        account_code: item.account_name,
      }));
      setFormData(prev => ({ ...prev, line_items: lineItems }));
    } else if (salesOrder && salesOrder.line_items) {
      // Load from sales order
      console.log('Loading line items from sales order:', salesOrder.line_items);
      const lineItems = salesOrder.line_items.map((item: any, index: number) => ({
        id: `line_${index}`,
        product_id: item.product_id,
        product_name: item.product_name,
        item_description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        discount_percent: item.discount_percent || 0,
        line_total: item.line_total,
        sales_tax_id: item.sales_tax,
        sales_tax_rate: item.sales_tax_rate || 0,
        sales_tax_amount: item.sales_tax_amount || 0,
        account_id: null, // Will be set when product is selected
        account_code: '',
      }));
      setFormData(prev => ({
        ...prev,
        line_items: lineItems,
        subtotal: salesOrder.subtotal || lineItems.reduce((sum, item) => sum + item.line_total, 0),
        tax_amount: salesOrder.tax_amount || lineItems.reduce((sum, item) => sum + (item.sales_tax_amount || 0), 0),
        total_amount: salesOrder.total_amount || 0,
      }));
    } else {
      // Initialize with one empty line item
      const emptyLineItem: CustomerBillLineItem = {
        id: 'line_0',
        product_id: null,
        product_name: '',
        item_description: '',
        quantity: 1,
        unit_price: 0,
        discount_percent: 0,
        line_total: 0,
        sales_tax_id: null,
        sales_tax_rate: 0,
        sales_tax_amount: 0,
        account_id: null,
        account_code: '',
      };
      setFormData(prev => ({ ...prev, line_items: [emptyLineItem] }));
    }
  }, [customerBill, salesOrder]);

  const handleInputChange = (field: keyof CustomerBillFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Auto-calculate due date when payment terms change
    if (field === 'payment_terms_id') {
      const selectedTerm = paymentTerms.find(term => term.id === value);
      if (selectedTerm) {
        const dueDate = dayjs(formData.bill_date).add(selectedTerm.net_days, 'day').format('YYYY-MM-DD');
        setFormData(prev => ({ 
          ...prev, 
          payment_terms_name: selectedTerm.name,
          due_date: dueDate 
        }));
      }
    }
  };

  const handleLineItemsChange = (lineItems: CustomerBillLineItem[]) => {
    // Calculate totals
    const subtotal = lineItems.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = lineItems.reduce((sum, item) => sum + (item.sales_tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    setFormData(prev => ({
      ...prev,
      line_items: lineItems,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
    }));
  };

  const handleSave = async (postImmediately = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      if (!formData.customer_id) {
        throw new Error('Please select a customer');
      }
      if (!formData.receivable_account_id) {
        throw new Error('Please select a receivable account');
      }
      if (formData.line_items.length === 0 || formData.line_items.every(item => !item.item_description)) {
        throw new Error('Please add at least one line item');
      }

      const billData = {
        customer_id: formData.customer_id,
        receivable_account_id: formData.receivable_account_id,
        bill_date: formData.bill_date,
        due_date: formData.due_date,
        reference_number: formData.reference_number,
        status: postImmediately ? 'posted' : formData.status,
        payment_terms_id: formData.payment_terms_id,
        notes: formData.notes,
        line_items: formData.line_items.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          description: item.item_description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_percent: item.discount_percent,
          line_total: item.line_total,
          sales_tax_id: item.sales_tax_id,
          sales_tax_rate: item.sales_tax_rate,
          sales_tax_amount: item.sales_tax_amount,
          account_id: item.account_id,
          account_name: item.account_code,
        })),
        subtotal: formData.subtotal,
        tax_amount: formData.tax_amount,
        total_amount: formData.total_amount,
        source_type: formData.source_type,
        source_document_id: formData.source_document_id,
      };

      let result;
      if (customerBill) {
        result = await customerBillService.updateCustomerBill(customerBill.id!, billData);
      } else {
        result = await customerBillService.createCustomerBill(billData);
      }

      setSuccess(`Customer bill ${customerBill ? 'updated' : 'created'} successfully!`);
      
      if (onClose) {
        setTimeout(() => onClose(), 1500);
      }

    } catch (err) {
      console.error('Error saving customer bill:', err);
      setError(err instanceof Error ? err.message : 'Failed to save customer bill');
    } finally {
      setLoading(false);
    }
  };

  const getCurrencySymbol = () => {
    return currencyInfo?.functional_currency_symbol || '$';
  };

  if (currencyLoading || customersLoading || paymentTermsLoading || accountsLoading || productsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper elevation={1} sx={{ p: 2, borderRadius: 0 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            {onClose && (
              <IconButton onClick={onClose}>
                <ArrowBackIcon />
              </IconButton>
            )}
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <ReceiptIcon />
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {viewMode ? 'View' : (customerBill ? 'Edit' : 'Create')} Customer Invoice
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {billNumber && `Invoice #${billNumber}`}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box display="flex" gap={1}>
            {!viewMode && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<SaveIcon />}
                  onClick={() => handleSave(false)}
                  disabled={loading}
                >
                  Save Draft
                </Button>
                <Button
                  variant="contained"
                  startIcon={<SaveAltIcon />}
                  onClick={() => handleSave(true)}
                  disabled={loading}
                >
                  Save & Post
                </Button>
              </>
            )}
            {viewMode && (
              <Button
                variant="outlined"
                startIcon={<PrintIcon />}
                onClick={() => console.log('Print invoice')}
              >
                Print
              </Button>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        <Grid container spacing={3}>
          {/* Customer and Account Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Customer Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Autocomplete
                      options={customers}
                      getOptionLabel={(option) => option.display_name || option.name || `Customer ${option.id}`}
                      value={customers.find(c => c.id === formData.customer_id) || null}
                      onChange={(_, newValue) => handleInputChange('customer_id', newValue?.id || null)}
                      disabled={viewMode}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer *"
                          required
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Autocomplete
                      options={accounts}
                      getOptionLabel={(option) => `${option.account_code} - ${option.account_name}`}
                      value={accounts.find(acc => acc.id === formData.receivable_account_id) || null}
                      onChange={(_, newValue) => handleInputChange('receivable_account_id', newValue?.id || null)}
                      disabled={viewMode}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Receivable Account *"
                          required
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Bill Details */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Invoice Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <StandardDatePicker
                      label="Invoice Date *"
                      value={dayjs(formData.bill_date)}
                      onChange={(newValue) => handleInputChange('bill_date', newValue?.format('YYYY-MM-DD') || '')}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <StandardDatePicker
                      label="Due Date *"
                      value={dayjs(formData.due_date)}
                      onChange={(newValue) => handleInputChange('due_date', newValue?.format('YYYY-MM-DD') || '')}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Reference Number"
                      value={formData.reference_number}
                      onChange={(e) => handleInputChange('reference_number', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Autocomplete
                      options={paymentTerms}
                      getOptionLabel={(option) => option.name}
                      value={paymentTerms.find(term => term.id === formData.payment_terms_id) || null}
                      onChange={(_, newValue) => handleInputChange('payment_terms_id', newValue?.id || null)}
                      disabled={viewMode}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Payment Terms"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      multiline
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Line Items */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Invoice Items
                </Typography>
                <CustomerBillLineTable
                  lineItems={formData.line_items}
                  onLineItemsChange={handleLineItemsChange}
                  products={products}
                  readOnly={viewMode}
                  currencySymbol={getCurrencySymbol()}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Totals */}
          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end">
              <Card sx={{ minWidth: 300 }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography>Subtotal:</Typography>
                    <Typography>{formatCurrency(formData.subtotal, getCurrencySymbol())}</Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography>Tax:</Typography>
                    <Typography>{formatCurrency(formData.tax_amount, getCurrencySymbol())}</Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6">{formatCurrency(formData.total_amount, getCurrencySymbol())}</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbars */}
      <Snackbar
        open={Boolean(success)}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
      >
        <Alert severity="success" onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={Boolean(error)}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateCustomerBillPage;
